package com.xyy.saas.localserver;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.redis.config.YudaoCacheAutoConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.eventbus.rocketmq.autoconfigure.EventBusRocketMQProperties;
import com.xyy.saas.inquiry.config.redis.LocalRedisConfig;
import com.xyy.saas.localserver.entity.mqtt.MqttServer;
import org.redisson.spring.starter.RedissonAutoConfigurationV2;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

@SpringBootApplication(scanBasePackages = {
                "com.xyy.saas.datasync",
                "com.xyy.common",
                "cn.iocoder.yudao.module",
                "com.xyy.saas.transmitter",
                "com.xyy.saas.inquiry",
                "com.xyy.saas.localserver",
                // "com.xyy.saas"
}, exclude = {
                YudaoCacheAutoConfiguration.class
                // YudaoWebSocketAutoConfiguration.class,
                // YudaoOperateLogConfiguration.class,
                // WxMaAutoConfiguration.class,
                // WxMpAutoConfiguration.class,

                // MybatisPlusInnerInterceptorAutoConfiguration.class,
                // LocalDBConfig.class,
})
@EnableDataSyncScan(scanBasePackages = {
                "cn.iocoder.yudao.module.*.dal.dataobject",
                "com.xyy.saas.localserver.entity",
                "com.xyy.saas.inquiry"
}, excludePackages = {
                "cn.iocoder.yudao.module.infra.dal.dataobject.demo",
                // com.xyy.saas.inquiry.util.RedisUtils 还未初始化spring上下文
                "com.xyy.saas.inquiry.util",
                // 云端dubbo 本地没有依赖
                "com.xyy.saas.inquiry.config",
}, baseEntity = { BaseDO.class })
@Import({ LocalRedisConfig.class, EventBusRocketMQProperties.class, RedissonAutoConfigurationV2.class,
                LocalserverApplication.PriorityBeanPostProcessor.class })

public class LocalserverApplication {
        public static void main(String[] args) {
                // 先写死 租户id
                String tenantId = System.getProperty("tenantId", "1");
                DataContextHolder.setTenantId(tenantId);

                SpringApplication.run(LocalserverApplication.class, args);
        }

        @Bean
        public MybatisPlusInterceptor mybatisPlusInterceptor() {
                return new MybatisPlusInterceptor();
        }

        static class PriorityBeanPostProcessor implements BeanFactoryPostProcessor {
                @Override
                public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {
                        // 将 Bean dataSyncTableInitializer 标记为优先初始化
                        beanFactory.getBeanDefinition("appAuthController").setDependsOn("dataSyncTableInitializer");
                }
        }

}